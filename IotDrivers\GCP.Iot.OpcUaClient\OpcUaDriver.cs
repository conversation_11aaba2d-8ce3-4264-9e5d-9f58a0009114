using GCP.Iot.Interfaces;
using GCP.Iot.Models;
using Opc.Ua;
using OpcUaHelper;
using Serilog;

namespace GCP.Iot.OpcUaClient
{
    [DriverInfo("用于连接OPC UA服务器的驱动")]
    public class OpcUaDriver : IDriver
    {
        private OpcUaClientHelper _client;

        public OpcUaDriver()
        {
            _client = new OpcUaClientHelper();
        }

        public bool IsConnected { get; set; } = false;

        private ILogger _logger;
        public ILogger Logger
        {
            get => _logger;
            set
            {
                _logger = value;
                _logger?.Information($"OPC UA驱动初始化完成");
            }
        }

        public string DriverCode => "OPCUaClient";
        public bool SupportsBatchReading => true;

        #region 配置参数

        [DriverParameter("最小采集周期(毫秒)")]
        public int MinSamplingPeriod { get; set; } = 1000;

        [DriverParameter("服务地址")]
        public string ServerUrl { get; set; } = "opc.tcp://localhost:4840/";

        [DriverParameter("读取Bad值")]
        public bool ReadBadValue { get; set; } = true;

        [DriverParameter("存档周期(毫秒)")]
        public int? ArchivePeriod { get; set; }

        #endregion

        public async Task<bool> ConnectAsync()
        {
            try
            {
                await _client.ConnectServer(ServerUrl);
                _logger?.Information("成功连接到OPC UA服务器: {S}", ServerUrl);
                IsConnected = true;
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> DisconnectAsync()
        {
            try
            {
                _client.Disconnect();
                _logger?.Information("已断开与OPC UA服务器的连接");
                IsConnected = false;
                return true;
            }
            catch (Exception ex)
            {
                _logger?.Error(ex, "断开OPC UA服务器连接失败");
                return await Task.FromResult(false);
            }
        }

        public void Dispose()
        {
            try
            {
                IsConnected = false;
                _client?.Disconnect();
                _client = null;
                GC.SuppressFinalize(this);

                _logger?.Information("OPC UA驱动已释放资源");
            }
            catch (Exception ex)
            {
                _logger?.Error(ex, "释放OPC UA驱动资源时发生错误");
            }
        }

        [DriverMethod("读OPCUa", description: "读OPCUa节点")]
        public async Task<DriverOperationResult> ReadAsync(string address, DataTypeEnum dataType)
        {
            var result = new DriverOperationResult
            {
                Status = OperationStatus.Success,
            };

            try
            {
                if (IsConnected)
                {
                    var dataValue = await _client.ReadNodeAsync(new NodeId(address));
                    if (ReadBadValue || DataValue.IsGood(dataValue))
                        result.RawValue = dataValue?.Value;
                }
                else
                {
                    result.Status = OperationStatus.NotConnected;
                    result.ErrorMessage = "未连接到OPC UA服务器，" + address;
                }
            }
            catch (Exception ex)
            {
                if (ex is ServiceResultException opcException)
                {
                    if (opcException.Result.StatusCode == StatusCodes.BadConnectionClosed)
                    {
                        IsConnected = false;
                    }
                }

                var message = $"读取地址 {address} 失败";
                _logger?.Error(ex, message + "，" + ex.Message);
                result.Status = OperationStatus.Failed;
                result.ErrorMessage = message;
            }
            return result;
        }

        [DriverMethod("批量读OPCUa", description: "批量读OPCUa节点")]
        public async Task<DriverOperationResult> BatchReadAsync(Dictionary<string, DataTypeEnum> addresses)
        {
            var result = new DriverOperationResult
            {
                Status = OperationStatus.Success,
            };

            try
            {
                if (IsConnected)
                {
                    var nodeIds = addresses.Select(t => new NodeId(t.Key)).ToArray();
                    var dataValues = await _client.ReadNodesAsync(nodeIds);
                    var values = new Dictionary<string, object>();
                    var i = 0;
                    foreach (var address in addresses)
                    {
                        var dataValue = dataValues[i];
                        if (ReadBadValue || DataValue.IsGood(dataValue))
                        {
                            values.Add(address.Key, dataValue?.Value);
                        }
                        else
                        {
                            values.Add(address.Key, null);
                        }
                        i++;
                    }

                    result.RawValue = values;
                }
                else
                {
                    result.Status = OperationStatus.NotConnected;
                    result.ErrorMessage = "未连接到OPC UA服务器";
                }
            }
            catch (Exception ex)
            {
                if (ex is ServiceResultException opcException)
                {
                    if (opcException.Result.StatusCode == StatusCodes.BadConnectionClosed)
                    {
                        IsConnected = false;
                    }
                }

                var message = $"读取地址 失败";
                _logger?.Error(ex, message + "，" + ex.Message);
                result.Status = OperationStatus.Failed;
                result.ErrorMessage = message;
            }
            return result;
        }

        public async Task<DriverOperationResult> WriteAsync(string address, object value, DataTypeEnum dataType)
        {
            var result = new DriverOperationResult
            {
                Status = OperationStatus.Success,
            };

            try
            {
                if (IsConnected)
                {
                    if (!await _client.WriteNodeAsync(address, value))
                    {
                        result.Status = OperationStatus.Failed;
                        result.ErrorMessage = $"写入失败: {value}";
                    }
                }
                else
                {
                    result.Status = OperationStatus.NotConnected;
                    result.ErrorMessage = "未连接到OPC UA服务器，" + address;
                }
            }
            catch (Exception ex)
            {
                var message = $"写入地址 {address} 失败";
                _logger?.Error(ex, message + ex.Message);
                result.Status = OperationStatus.Failed;
                result.ErrorMessage = message;
            }
            return result;
        }
    }
}