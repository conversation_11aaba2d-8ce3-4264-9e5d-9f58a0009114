using System.Collections.Concurrent;
using EasyCaching.Core;
using GCP.Iot.Interfaces;
using GCP.Iot.Models;
using Serilog;

namespace GCP.Iot.Services
{
    class EquipmentCommunicationManager : IDisposable
    {
        private readonly ConcurrentDictionary<string, EquipmentCommunicationTask> _equipmentTasks;
        private readonly ConcurrentDictionary<string, EquipmentTypeConfig> _equipmentTypeConfigs;
        private readonly CancellationTokenSource _globalCancellation;
        private readonly DriverManager _driverManager;
        private readonly IEasyCachingProvider _cachingProvider;
        public EquipmentEventManager EventManager { get; private set; }

        public EquipmentCommunicationManager(DriverManager driverManager, IEasyCachingProvider cachingProvider)
        {
            _equipmentTasks = new ConcurrentDictionary<string, EquipmentCommunicationTask>();
            _equipmentTypeConfigs = new ConcurrentDictionary<string, EquipmentTypeConfig>();
            _globalCancellation = new CancellationTokenSource();
            _driverManager = driverManager;
            _cachingProvider = cachingProvider;
            EventManager = new EquipmentEventManager();
        }

        /// <summary>
        /// 添加设备通信任务
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="equipmentCode">设备编码</param>
        /// <param name="equipmentType">设备类型</param>
        /// <param name="driver">设备驱动</param>
        /// <param name="variables">设备变量列表</param>
        public async Task AddEquipmentTaskAsync(string equipmentId, string equipmentCode, string equipmentType, IDriver driver,
            IEnumerable<EquipmentVariable> variables)
        {
            if (_equipmentTasks.ContainsKey(equipmentId))
            {
                Log.Warning("设备任务已存在: {EquipmentId} - {EquipmentCode}", equipmentId, equipmentCode);
                return;
            }

            var typeConfig = _equipmentTypeConfigs.GetOrAdd(equipmentType, new EquipmentTypeConfig
            {
                EquipmentType = equipmentType
            });

            // 判断是否使用共享驱动
            bool isSharedDriver = !_driverManager.HasEquipmentSpecificConfig(driver.DriverCode, equipmentId);
            if (isSharedDriver)
            {
                Log.Information("设备 {EquipmentCode} 使用共享驱动 {DriverDriverCode}", equipmentCode, driver.DriverCode);
            }

            var task = new EquipmentCommunicationTask(equipmentId, equipmentCode, equipmentType, driver, variables, typeConfig, EventManager, isSharedDriver, _cachingProvider);
            if (_equipmentTasks.TryAdd(equipmentId, task))
            {
                await task.StartAsync(_globalCancellation.Token);
                Log.Information("设备通信任务已启动: {EquipmentCode}", equipmentCode);
            }
        }

        /// <summary>
        /// 移除设备通信任务
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        public async Task<bool> RemoveEquipmentTaskAsync(string equipmentId, string equipmentCode)
        {
            if (_equipmentTasks.TryRemove(equipmentId, out var task))
            {
                await task.StopAsync();
                task.Dispose();
                Log.Information("设备通信任务已停止: {EquipmentCode}", equipmentCode);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 移除所有设备通信任务
        /// </summary>
        public async Task RemoveAllEquipmentTasksAsync()
        {
            foreach (var item in _equipmentTasks)
            {
                try
                {
                    await item.Value.StopAsync();
                    item.Value.Dispose();
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "停止设备 {ValueEquipmentCode} 通信任务异常", item.Value.EquipmentCode);
                }
            }

            _equipmentTasks.Clear();
        }

        /// <summary>
        /// 获取设备通信任务实例
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns>设备通信任务实例，如果不存在则返回null</returns>
        public EquipmentCommunicationTask GetEquipment(string equipmentId)
        {
            _equipmentTasks.TryGetValue(equipmentId, out var task);
            return task;
        }

        public List<EquipmentCommunicationTask> GetRunningEquipments()
        {
            return [.. _equipmentTasks.Values];
        }

        /// <summary>
        /// 获取特定驱动正在运行的设备列表
        /// </summary>
        public IEnumerable<EquipmentCommunicationTask> GetRunningEquipmentsByDriver(string driverCode)
        {
            return _equipmentTasks.Values.Where(t => t.DriverCode == driverCode);
        }

        /// <summary>
        /// 检查驱动是否有设备使用共享实例
        /// </summary>
        /// <param name="driverCode">驱动代码</param>
        /// <returns>true-有设备使用共享实例，false-没有设备使用共享实例</returns>
        public bool IsSharedDriverInUse(string driverCode)
        {
            return _equipmentTasks.Values.Any(t => t.DriverCode == driverCode && t.IsSharedDriver);
        }

        /// <summary>
        /// 获取设备通信任务
        /// </summary>
        public EquipmentCommunicationTask GetEquipmentTask(string equipmentId)
        {
            return _equipmentTasks.TryGetValue(equipmentId, out var task) ? task : null;
        }

        /// <summary>
        /// 获取设备状态
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        public bool IsEquipmentConnected(string equipmentId)
        {
            return _equipmentTasks.TryGetValue(equipmentId, out var task) && task.IsConnected;
        }

        /// <summary>
        /// 获取同类型设备的批量数据
        /// </summary>
        /// <param name="equipmentType">设备类型</param>
        /// <returns>设备ID和变量值的字典</returns>
        public async Task<Dictionary<string, Dictionary<string, object>>> GetBatchValuesAsync(string equipmentType)
        {
            if (_equipmentTypeConfigs.TryGetValue(equipmentType, out var typeConfig))
            {
                return await typeConfig.GetBatchValuesAsync();
            }
            return new Dictionary<string, Dictionary<string, object>>();
        }

        /// <summary>
        /// 写入设备变量
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="address">地址</param>
        /// <param name="value">值</param>
        /// <param name="dataType">数据类型</param>
        public async Task<DriverOperationResult> WriteVariableAsync(string equipmentId, string address, object value, DataTypeEnum dataType)
        {
            if (_equipmentTasks.TryGetValue(equipmentId, out var task))
            {
                return await task.WriteVariableAsync(address, value, dataType);
            }

            throw new ArgumentException($"设备任务不存在: {equipmentId}");
        }

        public void Dispose()
        {
            _globalCancellation.Cancel();
            foreach (var task in _equipmentTasks.Values)
            {
                task.StopAsync().Wait();
                task.Dispose();
            }
            _equipmentTasks.Clear();
            _equipmentTypeConfigs.Clear();
            _globalCancellation.Dispose();
        }
    }
}