using GCP.Common;
using GCP.DataAccess;
using GCP.Functions.Common.Models;
using GCP.Iot.Models;
using GCP.Iot.Services;
using LinqToDB;
using Serilog;
using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace GCP.Functions.Common.Services
{
    [Function("equipmentVariable", "设备变量管理服务")]
    class IotEquipmentVariableService : BaseService
    {
        private readonly EquipmentInitializer _equipmentInitializer = ServiceLocator.Current.GetService(typeof(EquipmentInitializer)) as EquipmentInitializer;
        private readonly EquipmentCommunicationManager _communicationManager = ServiceLocator.Current.GetService(typeof(EquipmentCommunicationManager)) as EquipmentCommunicationManager;

        [Function("getDataTypes", "获取数据类型")]
        public List<OptionVO> GetDataTypes()
        {
            var type = typeof(DataTypeEnum);
            var list = new List<OptionVO>();
            foreach (var field in type.GetFields(BindingFlags.Public | BindingFlags.Static))
            {
                var display = field.GetCustomAttribute<DisplayAttribute>();
                var option = new OptionVO()
                {
                    Value = field.Name,
                    Label = display?.Name ?? field.Name,
                };
                list.Add(option);
            }
            return list;
        }
        
        [Function("getById", "获取变量详情")]
        public LcIotEquipmentVariable GetById(string id)
        {
            using var db = this.GetDb();
            var data = db.LcIotEquipmentVariables.FirstOrDefault(t => t.Id == id);
            if (data == null)
            {
                throw new CustomException("未找到变量");
            }
            return data;
        }

        [Function("getAll", "获取变量清单")]
        public PagingData<LcIotEquipmentVariable> GetAll(string equipmentId, string keyword = null, int pageIndex = 1, int pageSize = 20)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcIotEquipmentVariables
                        where a.State == 1 &&
                        a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId &&
                        a.EquipmentId == equipmentId &&
                        (string.IsNullOrEmpty(keyword) || (a.VarName.Contains(keyword) || a.Description.Contains(keyword)))
                        orderby a.TimeCreate descending
                        select a).ToPagingData(pageIndex, pageSize);
            return data;
        }

        [Function("add", "新增变量")]
        public void Add(LcIotEquipmentVariable variable)
        {
            variable.SolutionId = this.SolutionId;
            variable.ProjectId = this.ProjectId;
            variable.VarName = variable.VarName.Trim();

            using var db = this.GetDb();
            var existedVariable = db.LcIotEquipmentVariables
                .FirstOrDefault(t => t.SolutionId == variable.SolutionId &&
                                     t.ProjectId == variable.ProjectId &&
                                     t.EquipmentId == variable.EquipmentId &&
                                     t.VarName == variable.VarName);

            if (existedVariable != null)
            {
                throw new CustomException($"变量名称 {variable.VarName} 已存在");
            }

            this.InsertData(variable);

            _ = _equipmentInitializer.RestartEquipmentAsync(variable.EquipmentId);
        }

        [Function("update", "更新变量")]
        public bool Update(LcIotEquipmentVariable variable)
        {
            using var db = this.GetDb();
            var data = db.LcIotEquipmentVariables.FirstOrDefault(t => t.Id == variable.Id);
            if (data == null)
            {
                return false;
            }

            variable.VarName = variable.VarName.Trim();

            var existedVariable = db.LcIotEquipmentVariables
                .FirstOrDefault(t => t.Id != variable.Id &&
                                     t.SolutionId == data.SolutionId &&
                                     t.ProjectId == data.ProjectId &&
                                     t.EquipmentId == variable.EquipmentId &&
                                     t.VarName == variable.VarName);

            if (existedVariable != null)
            {
                throw new CustomException($"变量名称 {variable.VarName} 已存在");
            }

            data.VarName = variable.VarName;
            data.Method = variable.Method;
            data.Address = variable.Address;
            data.Expressions = variable.Expressions;
            data.DataType = variable.DataType;
            data.ArchivePeriod = variable.ArchivePeriod;
            data.ChangeThreshold = variable.ChangeThreshold;
            data.IsUpload = variable.IsUpload;
            data.Description = variable.Description;

            this.UpdateData(data);

            _ = _equipmentInitializer.RestartEquipmentAsync(variable.EquipmentId);
            return true;
        }

        [Function("delete", "删除变量")]
        public bool Delete(string id)
        {
            using var db = this.GetDb();
            var data = db.LcIotEquipmentVariables.FirstOrDefault(t => t.Id == id);
            if (data == null)
            {
                return false;
            }
            data.State = 0;
            this.UpdateData(data);

            _ = _equipmentInitializer.RestartEquipmentAsync(data.EquipmentId);
            return true;
        }

        [Function("batchAdd", "批量新增变量")]
        public void BatchAdd(List<LcIotEquipmentVariable> variables)
        {
            if (variables == null || variables.Count == 0)
            {
                return;
            }

            using var db = this.GetDb();
            var equipmentId = variables[0].EquipmentId;
            var existedVariables = db.LcIotEquipmentVariables
                .Where(t => t.State == 1 &&
                           t.SolutionId == this.SolutionId &&
                           t.ProjectId == this.ProjectId &&
                           t.EquipmentId == equipmentId)
                .Select(t => t.VarName)
                .ToList();

            var duplicateVars = variables
                .Where(t => existedVariables.Contains(t.VarName))
                .Select(t => t.VarName)
                .ToList();

            if (duplicateVars.Any())
            {
                throw new CustomException($"以下变量名称已存在: {string.Join(", ", duplicateVars)}");
            }

            foreach (var variable in variables)
            {
                variable.SolutionId = this.SolutionId;
                variable.ProjectId = this.ProjectId;
                variable.IsUpload = 0;
                this.InsertData(variable, db);
            }
        }

        [Function("batchDelete", "批量删除变量")]
        public bool BatchDelete(string[] ids)
        {
            using var db = this.GetDb();
            var equipmentIds = db.LcIotEquipmentVariables
                .Where(t => ids.Contains(t.Id))
                .Select(t => t.EquipmentId)
                .Distinct()
                .ToList();
            if (equipmentIds.Count > 1)
            {
                throw new CustomException("不能批量操作不同设备的变量");
            }
            else if (equipmentIds.Count == 0)
            {
                throw new CustomException("未找到要操作的设备");
            }

            var data = db.LcIotEquipmentVariables
                .Where(t => ids.Contains(t.Id))
                .Set(t => t.State, (short)0);
            var rowsAffected = this.UpdateData(data);

            _ = _equipmentInitializer.RestartEquipmentAsync(equipmentIds.First());
            return rowsAffected > 0;
        }

        [Function("updateUploadStatus", "更新变量上传状态")]
        public bool UpdateUploadStatus(string[] ids, short isUpload)
        {
            using var db = this.GetDb();
            var equipmentIds = db.LcIotEquipmentVariables
                .Where(t => ids.Contains(t.Id))
                .Select(t => t.EquipmentId)
                .Distinct()
                .ToList();
            if (equipmentIds.Count > 1)
            {
                throw new CustomException("不能批量操作不同设备的变量");
            }
            else if (equipmentIds.Count == 0)
            {
                throw new CustomException("未找到要操作的设备");
            }

            var data = db.LcIotEquipmentVariables
                .Where(t => ids.Contains(t.Id))
                .Set(t => t.IsUpload, isUpload);
            var rowsAffected = this.UpdateData(data);

            _ = _equipmentInitializer.RestartEquipmentAsync(equipmentIds.First());
            return rowsAffected > 0;
        }


        [Function("writeValue", "写入设备变量值")]
        public async Task WriteValue(string equipmentId, string address, object value, DataTypeEnum dataType)
        {
            var valueStr = value?.ToString();
            if (string.IsNullOrEmpty(valueStr))
            {
                throw new CustomException("写入值不能为空");
            }

            var equipmentTask = _communicationManager.GetEquipmentTask(equipmentId);
            if (equipmentTask != null)
            {
                var result = await equipmentTask.WriteVariableAsync(address, valueStr, dataType);
                if (result.Status != OperationStatus.Success)
                {
                    throw new CustomException(result.ErrorMessage);
                }
            }
        }

        [Function("getRealTimeValues", "获取设备变量实时值")]
        public Dictionary<string, object> GetRealTimeValues(string equipmentId)
        {
            try
            {
                // 直接从设备通信任务中获取实时数据
                var equipmentTask = _communicationManager.GetEquipmentTask(equipmentId);
                if (equipmentTask != null)
                {
                    return equipmentTask.GetCurrentValues();
                }
                
                return new Dictionary<string, object>();
            }
            catch (Exception ex)
            {
                // 捕获异常并记录日志
                Log.Error(ex, "获取设备变量实时值异常: {EquipmentId}", equipmentId);
                return new Dictionary<string, object>();
            }
        }
        
        [Function("getRealTimeValue", "获取单个变量实时值")]
        public object GetRealTimeValue(string equipmentId, string variableName)
        {
            try
            {
                // 直接从设备通信任务中获取实时数据
                var equipmentTask = _communicationManager.GetEquipmentTask(equipmentId);
                if (equipmentTask != null)
                {
                    // 使用GetVariableValue方法
                    return equipmentTask.GetVariableValue(variableName);
                }
                
                return null;
            }
            catch (Exception ex)
            {
                // 捕获异常并记录日志
                Log.Error(ex, "获取单个变量实时值异常: {EquipmentId}.{VariableName}", equipmentId, variableName);
                return null;
            }
        }
        
        [Function("getMultiRealTimeValues", "批量获取多个设备变量实时值")]
        public Dictionary<string, Dictionary<string, object>> GetMultiRealTimeValues(string[] equipmentIds)
        {
            var result = new Dictionary<string, Dictionary<string, object>>();
            
            foreach (var equipmentId in equipmentIds)
            {
                var values = GetRealTimeValues(equipmentId);
                if (values != null && values.Count > 0)
                {
                    result[equipmentId] = values;
                }
            }
            
            return result;
        }
    }
}