using GCP.Common;
using GCP.DataAccess;
using GCP.Iot.Services;

namespace GCP.Functions.Common.Services
{
    [Function("equipment", "设备管理服务")]
    class IotEquipmentService : BaseService
    {
        private readonly EquipmentInitializer _equipmentInitializer = ServiceLocator.Current.GetService(typeof(EquipmentInitializer)) as EquipmentInitializer;
        private readonly EquipmentCommunicationManager _communicationManager = ServiceLocator.Current.GetService(typeof(EquipmentCommunicationManager)) as EquipmentCommunicationManager;

        [Function("getById", "获取设备详情")]
        public LcIotEquipment GetById(string id)
        {
            using var db = this.GetDb();
            var data = db.LcIotEquipment.FirstOrDefault(t => t.Id == id);
            if (data == null)
            {
                throw new CustomException("未找到设备");
            }
            return data;
        }

        [Function("getAll", "获取设备清单")]
        public PagingData<LcIotEquipment> GetAll(string keyword = null, string equipmentType = null, int pageIndex = 1, int pageSize = 20)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcIotEquipment
                        where a.State == 1 &&
                       a.SolutionId == this.SolutionId &&
                       a.ProjectId == this.ProjectId &&
                       (string.IsNullOrEmpty(keyword) || (a.EquipmentName.Contains(keyword) || a.EquipmentCode.Contains(keyword))) &&
                       (string.IsNullOrEmpty(equipmentType) || a.EquipmentType == equipmentType)
                        orderby a.TimeCreate descending
                        select a).ToPagingData(pageIndex, pageSize);
            return data;
        }

        [Function("add", "新增设备")]
        public void Add(LcIotEquipment equipment)
        {
            equipment.SolutionId = this.SolutionId;
            equipment.ProjectId = this.ProjectId;

            using var db = this.GetDb();
            var existedEquipment = db.LcIotEquipment
                .FirstOrDefault(t => t.SolutionId == equipment.SolutionId &&
                                     t.ProjectId == equipment.ProjectId &&
                                     (t.EquipmentCode == equipment.EquipmentCode || t.EquipmentName == equipment.EquipmentName));

            if (existedEquipment != null)
            {
                if (existedEquipment.EquipmentCode == equipment.EquipmentCode)
                    throw new CustomException($"设备编码 {equipment.EquipmentCode} 已存在");
                else
                    throw new CustomException($"设备名称 {equipment.EquipmentName} 已存在");
            }

            this.InsertData(equipment);
        }

        [Function("update", "更新设备")]
        public bool Update(LcIotEquipment equipment)
        {
            using var db = this.GetDb();
            var data = db.LcIotEquipment.FirstOrDefault(t => t.Id == equipment.Id);
            if (data == null)
            {
                return false;
            }

            var existedEquipment = db.LcIotEquipment
                .FirstOrDefault(t => t.Id != equipment.Id &&
                                     t.SolutionId == data.SolutionId &&
                                     t.ProjectId == data.ProjectId &&
                                     (t.EquipmentCode == equipment.EquipmentCode || t.EquipmentName == equipment.EquipmentName));

            if (existedEquipment != null)
            {
                if (existedEquipment.EquipmentCode == equipment.EquipmentCode)
                    throw new CustomException($"设备编码 {equipment.EquipmentCode} 已存在");
                else
                    throw new CustomException($"设备名称 {equipment.EquipmentName} 已存在");
            }
            var isNewCode = data.EquipmentCode != equipment.EquipmentCode;

            data.OrgCode = equipment.OrgCode;
            data.EquipmentCode = equipment.EquipmentCode;
            data.EquipmentName = equipment.EquipmentName;
            data.Description = equipment.Description;
            data.EquipmentType = equipment.EquipmentType;
            data.DriverCode = equipment.DriverCode;

            this.UpdateData(data);

            if (isNewCode)
                _ = _equipmentInitializer.RestartEquipmentAsync(data.Id);
            return true;
        }

        [Function("delete", "删除设备")]
        public bool Delete(string id)
        {
            using var db = this.GetDb();
            var data = db.LcIotEquipment.FirstOrDefault(t => t.Id == id);
            if (data == null)
            {
                return false;
            }
            data.State = 0;
            this.UpdateData(data);

            _ = _equipmentInitializer.StopEquipmentAsync(id);
            return true;
        }

        [Function("updateStatus", "更新设备状态")]
        public async Task<bool> UpdateStatusAsync(string[] ids, short status)
        {
            var tasks = ids
                .Select(id => status == 1 ? _equipmentInitializer.InitializeEquipmentAsync(id) : _equipmentInitializer.StopEquipmentAsync(id))
                .ToList();
            await Task.WhenAll(tasks);
            
            return true;
        }

        [Function("getByType", "获取指定类型的设备列表")]
        public List<LcIotEquipment> GetByType(string equipmentType)
        {
            using var db = this.GetDb();
            var data = db.LcIotEquipment
                .Where(t => t.State == 1 &&
                           t.SolutionId == this.SolutionId &&
                           t.ProjectId == this.ProjectId &&
                           t.EquipmentType == equipmentType)
                .ToList();
            return data;
        }

        [Function("getTypes", "获取所有设备类型")]
        public List<string> GetTypes(string orgCode = null)
        {
            using var db = this.GetDb();
            var data = db.LcIotEquipment
                .Where(t => t.State == 1 &&
                           t.SolutionId == this.SolutionId &&
                           t.ProjectId == this.ProjectId &&
                           t.EquipmentType != null &&
                           (string.IsNullOrEmpty(orgCode) || t.OrgCode == orgCode))
                .Select(t => t.EquipmentType)
                .Distinct()
                .ToList();
            return data;
        }

        [Function("getOrgCodes", "获取所有组织编码")]
        public List<string> GetOrgCodes()
        {
            using var db = this.GetDb();
            var data = db.LcIotEquipment
                .Where(t => t.State == 1 &&
                           t.SolutionId == this.SolutionId &&
                           t.ProjectId == this.ProjectId &&
                           t.OrgCode != null)
                .Select(t => t.OrgCode)
                .Distinct()
                .ToList();
            return data;
        }

        [Function("getOnlineStatus", "获取设备在线状态")]
        public bool GetOnlineStatus(string equipmentId)
        {
            if (string.IsNullOrEmpty(equipmentId))
            {
                return false;
            }

            return _communicationManager?.IsEquipmentOnline(equipmentId) ?? false;
        }

        [Function("getMultiOnlineStatus", "批量获取设备在线状态")]
        public Dictionary<string, bool> GetMultiOnlineStatus(string[] equipmentIds)
        {
            if (equipmentIds == null || equipmentIds.Length == 0)
            {
                return new Dictionary<string, bool>();
            }

            return _communicationManager?.GetEquipmentsOnlineStatus(equipmentIds) ?? new Dictionary<string, bool>();
        }
    }
}