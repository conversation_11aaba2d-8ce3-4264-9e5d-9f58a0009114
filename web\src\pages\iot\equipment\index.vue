<template>
  <div style="height: 100%">
    <equipment-form
      v-if="isShowForm"
      :data="currentRowData"
      :is-edit="isEditForm"
      @back="onClickBackForm"
      @submit="onClickSubmitForm"
    />
    <CmpContainer v-show="!isShowForm" full>
      <CmpCard>
        <t-row justify="space-between">
          <div class="left-operation-container">
            <t-button @click="onClickAdd">新建设备</t-button>
            <t-button
              variant="base"
              theme="default"
              :disabled="!selectedRowKeys.length"
              @click="onClickUpdateStatus(1)"
            >
              启用设备
            </t-button>
            <t-button
              variant="base"
              theme="default"
              :disabled="!selectedRowKeys.length"
              @click="onClickUpdateStatus(0)"
            >
              停用设备
            </t-button>
            <p v-if="!!selectedRowKeys.length" class="selected-count">
              {{ t('pages.listBase.select') }} {{ selectedRowKeys.length }} {{ t('pages.listBase.items') }}
            </p>
          </div>
          <div class="right-operation-container">
            <t-select v-model="equipmentType" class="type-select" placeholder="设备类型" clearable @change="fetchData">
              <t-option v-for="type in equipmentTypes" :key="type" :value="type" :label="type" />
            </t-select>
            <t-input v-model="searchValue" placeholder="请输入设备名称或编码搜索" clearable @enter="fetchData">
              <template #suffix-icon>
                <search-icon size="16px" />
              </template>
            </t-input>
          </div>
        </t-row>
        <t-table
          :data="data"
          :columns="COLUMNS"
          size="small"
          height="calc(50vh - 150px)"
          row-key="id"
          :hover="true"
          :pagination="pagination"
          :selected-row-keys="selectedRowKeys"
          :loading="dataLoading"
          @page-change="onPageChange"
          @select-change="onSelectChange"
          @row-click="onTableClick"
        >
          <template #status="{ row }">
            <t-tag v-if="row.status === 0" theme="warning" variant="light-outline">
              <pause-circle-filled-icon />
              停用
            </t-tag>
            <t-tag v-if="row.status === 1" theme="success" variant="light-outline">
              <play-circle-filled-icon />
              启用
            </t-tag>
          </template>

          <template #operate="{ row }">
            <t-space size="small">
              <t-link theme="primary" @click="onClickEdit(row)">编辑</t-link>
              <t-popconfirm content="确认删除吗" @confirm="onConfirmDelete(row)">
                <t-link theme="danger">删除</t-link>
              </t-popconfirm>
            </t-space>
          </template>
        </t-table>

        <div class="driver-params">
          <t-divider
            ><span style="font-weight: 700; color: var(--td-brand-color)"
              >{{ selectedEquipment?.equipmentCode || '' }} - {{ selectedEquipment?.equipmentName }}</span
            >
            - 驱动属性</t-divider
          >
          <t-table
            row-key="id"
            :data="driverParams"
            :columns="DRIVER_COLUMNS"
            :loading="driverParamsLoading"
            size="small"
            height="calc(50vh - 160px)"
          >
            <template #paramScope="{ row }">
              <t-select
                v-model="row.scope"
                :options="[
                  { label: '全局', value: 'global' },
                  { label: '设备', value: 'equipment' },
                ]"
                @change="(val) => onScopeChange(val, row)"
              />
            </template>
            <template #paramValue="{ row }">
              <t-input v-model.trim="row.paramValue" @input="() => onParamValueChange(row)" />
            </template>
          </t-table>
        </div>
      </CmpCard>
    </CmpContainer>
  </div>
</template>

<script lang="ts">
export default {
  name: 'Equipment',
};
</script>

<script setup lang="ts">
import { debounce } from 'lodash';
import { PauseCircleFilledIcon, PlayCircleFilledIcon, SearchIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin, PrimaryTableCol, TableProps, TableRowData } from 'tdesign-vue-next';
import { onMounted, ref } from 'vue';

import { api, Services } from '@/api/system';
import { t } from '@/locales';

import EquipmentForm from './EquipmentForm.vue';

const isShowForm = ref(false);
const selectedRowKeys = ref<Array<string | number>>([]);
const searchValue = ref('');
const equipmentType = ref('');
const equipmentTypes = ref<string[]>([]);

const COLUMNS: PrimaryTableCol<TableRowData>[] = [
  { colKey: 'row-select', type: 'multiple', width: 64, fixed: 'left' },
  {
    title: '组织编码',
    ellipsis: true,
    colKey: 'orgCode',
  },
  {
    title: '设备编码',
    ellipsis: true,
    colKey: 'equipmentCode',
  },
  {
    title: '设备名称',
    align: 'left',
    colKey: 'equipmentName',
    ellipsis: true,
  },
  {
    title: '设备类型',
    ellipsis: true,
    colKey: 'equipmentType',
  },
  {
    title: '驱动编码',
    colKey: 'driverCode',
  },
  {
    title: '状态',
    colKey: 'status',
  },
  {
    title: '描述',
    colKey: 'description',
    ellipsis: true,
  },
  {
    title: '操作',
    align: 'left',
    fixed: 'right',
    width: 160,
    colKey: 'operate',
  },
];

const data = ref([]);
const pagination = ref({
  pageSize: 20,
  total: 0,
  current: 1,
});

const dataLoading = ref(false);
const fetchData = async () => {
  dataLoading.value = true;
  try {
    const { list, total } = await api.run(Services.equipmentGetAll, {
      keyword: searchValue.value,
      equipmentType: equipmentType.value,
      pageIndex: pagination.value.current,
      pageSize: pagination.value.pageSize,
    });
    data.value = list;
    pagination.value = {
      ...pagination.value,
      total,
    };
  } catch (e) {
    console.log(e);
  } finally {
    dataLoading.value = false;
  }
};

const fetchEquipmentTypes = async () => {
  try {
    const types = await api.run(Services.equipmentGetTypes);
    equipmentTypes.value = types;
  } catch (e) {
    console.log(e);
  }
};

onMounted(() => {
  fetchData();
  fetchEquipmentTypes();
});

const onSelectChange: TableProps['onSelectChange'] = (value) => {
  selectedRowKeys.value = value;
};

const onPageChange: TableProps['onPageChange'] = (pageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  fetchData();
};

const onConfirmDelete = (row) => {
  api.run(Services.equipmentDelete, { id: row.id }).then(() => {
    fetchData();
    MessagePlugin.success('删除成功');
  });
};

const currentRowData = ref({});
const isEditForm = ref(false);
const onClickEdit = (row) => {
  isEditForm.value = true;
  currentRowData.value = row;
  isShowForm.value = true;
};

const onClickAdd = () => {
  isEditForm.value = false;
  isShowForm.value = true;
  currentRowData.value = null;
};

const onClickUpdateStatus = (status: number) => {
  api.run(Services.equipmentUpdateStatus, { ids: selectedRowKeys.value, status }).then(() => {
    fetchData();
    MessagePlugin.success('操作成功');
  });
};

const onClickBackForm = () => {
  isShowForm.value = false;
  fetchData();
};

const onClickSubmitForm = () => {
  isShowForm.value = false;
  fetchData();
};

const selectedEquipment = ref(null);
const driverParams = ref([]);
const driverParamsLoading = ref(false);

const DRIVER_COLUMNS = [
  { colKey: 'paramKey', title: '参数键', width: 200 },
  { colKey: 'scope', title: '作用域', width: 150, cell: 'paramScope' },
  {
    colKey: 'paramValue',
    title: '参数值',
  },
  { colKey: 'description', title: '描述' },
];

const onTableClick = ({ e, row }) => {
  if (e?.target?.closest('.t-space')) {
    return;
  }

  console.log('点击设备行:', row);
  selectedEquipment.value = row;
  loadDriverParams(row);
};

const loadDriverParams = async (data) => {
  const equipment = data;

  driverParamsLoading.value = true;
  try {
    const params = await api.run(Services.driverGetAll, {
      driverCode: equipment.driverCode,
      equipmentId: equipment.id,
    });
    console.log('获取到的驱动参数:', params);

    driverParams.value = params.map((param) => ({
      ...param,
      scope: param.equipmentId ? 'equipment' : 'global',
    }));
  } catch (error) {
    console.error('加载驱动参数失败:', error);
    MessagePlugin.error('获取驱动参数失败');
  } finally {
    driverParamsLoading.value = false;
  }
};

const onScopeChange = debounce(async (scope, row) => {
  try {
    if (scope === 'equipment') {
      await api.run(Services.driverCopyGlobalParams, {
        driverCode: selectedEquipment.value.driverCode,
        equipmentId: selectedEquipment.value.id,
        key: row.paramKey,
      });
      MessagePlugin.success('复制到设备成功');
    } else if (scope === 'global') {
      await api.run(Services.driverDeleteEquipmentParams, {
        id: row.id,
        driverCode: selectedEquipment.value.driverCode,
        equipmentId: selectedEquipment.value.id,
      });
      MessagePlugin.success('已删除设备参数，使用全局参数');
    }
    loadDriverParams(selectedEquipment.value);
  } catch (error) {
    MessagePlugin.error('操作失败');
    row.scope = scope === 'global' ? 'global' : 'equipment';
  }
}, 1000);

const onParamValueChange = debounce(async (row) => {
  try {
    await api.run(Services.driverUpdate, row);
    MessagePlugin.success('更新成功');
    loadDriverParams(selectedEquipment.value);
  } catch (error) {
    MessagePlugin.error('更新失败');
    console.error('更新失败:', error);
  }
}, 500);
</script>

<style lang="less" scoped>
.left-operation-container {
  display: flex;
  align-items: center;
  margin-bottom: var(--td-comp-margin-xxl);

  .selected-count {
    display: inline-block;
    margin-left: var(--td-comp-margin-l);
    color: var(--td-text-color-secondary);
  }
}

.right-operation-container {
  display: flex;
  align-items: center;
  margin-bottom: var(--td-comp-margin-xxl);
  gap: var(--td-comp-margin-s);

  .type-select {
    width: 200px;
  }

  .t-input {
    width: 360px;
  }
}

.driver-params {
  margin-top: 16px;
}
</style>
