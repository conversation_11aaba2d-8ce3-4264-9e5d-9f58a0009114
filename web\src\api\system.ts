import { http } from '@/utils/request';

export enum Services {
  apiGetAll = '/gcp/api/getAll',
  apiGetApiById = '/gcp/api/getApiById',
  apiAdd = '/gcp/api/add',
  apiUpdate = '/gcp/api/update',
  apiDelete = '/gcp/api/delete',
  apiGetResponseList = '/gcp/api/getResponseList',
  apiGetResponseById = '/gcp/api/getResponseById',
  apiSaveResponse = '/gcp/api/saveResponse',
  apiDeleteResponse = '/gcp/api/deleteResponse',
  apiValidateApiResponse = '/gcp/api/validateApiResponse',
  dataDictionaryGet = '/gcp/dataDictionary/get',
  dataDictionaryGetAll = '/gcp/dataDictionary/getAll',
  dataDictionaryGetByCode = '/gcp/dataDictionary/getByCode',
  dataDictionaryGetByGroupCode = '/gcp/dataDictionary/getByGroupCode',
  dataDictionaryGetVariablesByDirCode = '/gcp/dataDictionary/getVariablesByDirCode',
  dataDictionaryGetOptionsByGroupCode = '/gcp/dataDictionary/getOptionsByGroupCode',
  dataDictionarySave = '/gcp/dataDictionary/save',
  dataDictionaryRemove = '/gcp/dataDictionary/remove',
  dataDictionarySaveListByGroup = '/gcp/dataDictionary/saveListByGroup',
  dataSourceAdd = '/gcp/dataSource/add',
  dataSourceDelete = '/gcp/dataSource/delete',
  dataSourceGetAll = '/gcp/dataSource/getAll',
  dataSourceGetById = '/gcp/dataSource/getById',
  dataSourceSearchTableSchemas = '/gcp/dataSource/searchTableSchemas',
  dataSourceSearch = '/gcp/dataSource/search',
  dataSourceUpdate = '/gcp/dataSource/update',
  dataSourceChangeState = '/gcp/dataSource/changeState',
  dataSourceMove = '/gcp/dataSource/move',
  dataSourceTestConnection = '/gcp/dataSource/testConnection',
  dataSourceTestConnectionById = '/gcp/dataSource/testConnectionById',
  equipmentGetAll = '/gcp/equipment/getAll',
  equipmentGetTypes = '/gcp/equipment/getTypes',
  equipmentGetOrgCodes = '/gcp/equipment/getOrgCodes',
  equipmentAdd = '/gcp/equipment/add',
  equipmentUpdate = '/gcp/equipment/update',
  equipmentDelete = '/gcp/equipment/delete',
  equipmentUpdateStatus = '/gcp/equipment/updateStatus',
  equipmentGetOnlineStatus = '/gcp/equipment/getOnlineStatus',
  equipmentGetMultiOnlineStatus = '/gcp/equipment/getMultiOnlineStatus',
  driverGetDriverCodes = '/gcp/driver/getDriverCodes',
  driverGetGlobalParams = '/gcp/driver/getGlobalParams',
  driverGetAll = '/gcp/driver/getAll',
  driverUpdate = '/gcp/driver/update',
  driverCopyGlobalParams = '/gcp/driver/copyGlobalParams',
  driverDeleteEquipmentParams = '/gcp/driver/deleteEquipmentParams',
  equipmentVariableGetDataTypes = '/gcp/equipmentVariable/getDataTypes',
  equipmentVariableGetAll = '/gcp/equipmentVariable/getAll',
  equipmentVariableAdd = '/gcp/equipmentVariable/add',
  equipmentVariableUpdate = '/gcp/equipmentVariable/update',
  equipmentVariableDelete = '/gcp/equipmentVariable/delete',
  equipmentVariableBatchAdd = '/gcp/equipmentVariable/batchAdd',
  equipmentVariableBatchDelete = '/gcp/equipmentVariable/batchDelete',
  equipmentVariableWriteValue = '/gcp/equipmentVariable/writeValue',
  equipmentVariableUpdateUploadStatus = '/gcp/equipmentVariable/updateUploadStatus',
  equipmentVariableGetRealTimeValues = '/gcp/equipmentVariable/getRealTimeValues',
  equipmentVariableGetRealTimeValue = '/gcp/equipmentVariable/getRealTimeValue',
  flowHistoryCleanupFlowLogs = '/gcp/flowHistory/cleanupFlowLogs',
  flowHistoryGetAll = '/gcp/flowHistory/getAll',
  flowHistoryGetStepData = '/gcp/flowHistory/getStepData',
  flowHistoryGetSteps = '/gcp/flowHistory/getSteps',
  flowRunGetAll = '/gcp/flowRun/getAll',
  flowRunGetFlowLogs = '/gcp/flowRun/getFlowLogs',
  flowRunGetStepData = '/gcp/flowRun/getStepData',
  flowRunGetSteps = '/gcp/flowRun/getSteps',
  flowRunDeleteErrorLogs = '/gcp/flowRun/deleteErrorLogs',
  flowRunTransferFlowDataToLogs = '/gcp/flowRun/transferFlowDataToLogs',
  flowRunRetry = '/gcp/flowRun/retry',
  flowRunContinue = '/gcp/flowRun/continue',
  functionGet = '/gcp/function/get',
  functionGetByIds = '/gcp/function/getByIds',
  functionGetAll = '/gcp/function/getAll',
  functionGetVersion = '/gcp/function/getVersion',
  functionCodeGetAll = '/gcp/functionCode/getAll',
  functionCodeGetCodeByVersion = '/gcp/functionCode/getCodeByVersion',
  functionCodeSaveCode = '/gcp/functionCode/saveCode',
  jobAdd = '/gcp/job/add',
  jobCleanupLogs = '/gcp/job/cleanupLogs',
  jobCronInfo = '/gcp/job/cronInfo',
  jobDelete = '/gcp/job/delete',
  jobGetById = '/gcp/job/getById',
  jobGetAll = '/gcp/job/getAll',
  jobGetAllGroup = '/gcp/job/getAllGroup',
  jobLogs = '/gcp/job/logs',
  jobRefresh = '/gcp/job/refresh',
  jobTrigger = '/gcp/job/trigger',
  jobUpdate = '/gcp/job/update',
  jobUpdateFunction = '/gcp/job/updateFunction',
  jobUpdateState = '/gcp/job/updateState',
  projectAdd = '/gcp/project/add',
  projectDelete = '/gcp/project/delete',
  projectGetAll = '/gcp/project/getAll',
  projectGetById = '/gcp/project/getById',
  projectUpdate = '/gcp/project/update',
  proxyAdd = '/gcp/proxy/add',
  proxyRefresh = '/gcp/proxy/refresh',
  publishAdd = '/gcp/publish/add',
  publishDelete = '/gcp/publish/delete',
  publishGetAll = '/gcp/publish/getAll',
  publishGetById = '/gcp/publish/getById',
  publishUpdate = '/gcp/publish/update',
  solutionAdd = '/gcp/solution/add',
  solutionDelete = '/gcp/solution/delete',
  solutionGetAll = '/gcp/solution/getAll',
  solutionUpdate = '/gcp/solution/update',
  userAdd = '/gcp/user/add',
  userCurrentInfo = '/gcp/user/currentInfo',
  userDelete = '/gcp/user/delete',
  userGetAll = '/gcp/user/getAll',
  userLogin = '/gcp/user/login',
  userLogout = '/gcp/user/logout',
  userUpdate = '/gcp/user/update',
  userUpdatePassword = '/gcp/user/updatePassword',
  license = '/gcp/license',
  licenseActivate = '/gcp/license/activate',
  dirTreeGetAll = '/gcp/dirTree/getAll',
  dirTreeGet = '/gcp/dirTree/get',
  dirTreeGetByCode = '/gcp/dirTree/getByCode',
  dirTreeGetChildren = '/gcp/dirTree/getChildren',
  dirTreeSave = '/gcp/dirTree/save',
  dirTreeDelete = '/gcp/dirTree/delete',
  dirTreeMove = '/gcp/dirTree/move',
  dirTreeGetTree = '/gcp/dirTree/getTree',
  dirTreeGetTreeItem = '/gcp/dirTree/getTreeItem',
  dirTreeGetLeafList = '/gcp/dirTree/getLeafList',
  messageSubscriptionGetAllEvents = '/gcp/messageSubscription/getAllEvents',
  messageSubscriptionGetEventById = '/gcp/messageSubscription/getEventById',
  messageSubscriptionSaveEvent = '/gcp/messageSubscription/saveEvent',
  messageSubscriptionRemoveEvent = '/gcp/messageSubscription/removeEvent',
  messageSubscriptionUpdateEventStatus = '/gcp/messageSubscription/updateEventStatus',
  messageSubscriptionGetMappingsByEventId = '/gcp/messageSubscription/getMappingsByEventId',
  messageSubscriptionSaveMapping = '/gcp/messageSubscription/saveMapping',
  messageSubscriptionRemoveMapping = '/gcp/messageSubscription/removeMapping',
  messageSubscriptionGetEquipments = '/gcp/messageSubscription/getEquipments',
  messageSubscriptionGetEquipmentTypes = '/gcp/messageSubscription/getEquipmentTypes',
  messageSubscriptionGetEquipmentVariables = '/gcp/messageSubscription/getEquipmentVariables',
  messageEventGetById = '/gcp/event/getById',
  messageEventGetAll = '/gcp/event/getAll',
  messageEventAdd = '/gcp/event/add',
  messageEventUpdate = '/gcp/event/update',
  messageEventDelete = '/gcp/event/delete',
  messageEventUpdateState = '/gcp/event/updateState',
  messageEventGetMappings = '/gcp/event/getMappings',
  messageEventGetEquipmentInfoByEventId = '/gcp/event/getEquipmentInfoByEventId',
  messageEventAddMapping = '/gcp/event/addMapping',
  messageEventAddMappingByEquipment = '/gcp/event/addMappingByEquipment',
  messageEventDeleteMapping = '/gcp/event/deleteMapping',
  messageEventGetByDirCode = '/gcp/event/getByDirCode',
  apiGetFunctionNames = '/gcp/api/getFunctionNames',
  jobGetFunctionNames = '/gcp/job/getFunctionNames',
  eventGetFunctionNames = '/gcp/event/getFunctionNames',
  dashboardGetTopPanelStats = '/gcp/dashboard/getTopPanelStats',
  dashboardGetServiceResources = '/gcp/dashboard/getServiceResources',
  dashboardGetChartData = '/gcp/dashboard/getChartData',
  dashboardGetCallRank = '/gcp/dashboard/getCallRank',
  dashboardGetErrorRank = '/gcp/dashboard/getErrorRank',
  // 可视化函数相关API
  visualFunctionExecute = '/gcp/visualFunction/execute',
  visualFunctionTestStep = '/gcp/visualFunction/testStep',
  visualFunctionGetBuiltinFunctions = '/gcp/visualFunction/getBuiltinFunctions',
  visualFunctionValidate = '/gcp/visualFunction/validate',

  // 动作库相关API
  actionLibraryGetAll = '/gcp/actionLibrary/getAll',
  actionLibraryGet = '/gcp/actionLibrary/get',
  actionLibrarySave = '/gcp/actionLibrary/save',
  actionLibraryDelete = '/gcp/actionLibrary/delete',
  actionLibraryGetCategories = '/gcp/actionLibrary/getCategories',
  actionLibraryCopy = '/gcp/actionLibrary/copy',
  actionLibraryUpdateStatus = '/gcp/actionLibrary/updateStatus',
  actionLibraryGetExecutionStats = '/gcp/actionLibrary/getExecutionStats',
  actionLibraryTestExecute = '/gcp/actionLibrary/testExecute',
  actionLibraryGetExecutionLogs = '/gcp/actionLibrary/getExecutionLogs',

  // 动作库执行相关API
  actionLibraryExecutionExecute = '/gcp/actionLibraryExecution/execute',
  actionLibraryExecutionExecuteWithResult = '/gcp/actionLibraryExecution/executeWithResult',
  actionLibraryExecutionExecuteBatch = '/gcp/actionLibraryExecution/executeBatch',
  actionLibraryExecutionExecuteByName = '/gcp/actionLibraryExecution/executeByName',
  actionLibraryExecutionExecuteConditional = '/gcp/actionLibraryExecution/executeConditional',
  actionLibraryExecutionExecuteWithTimeout = '/gcp/actionLibraryExecution/executeWithTimeout',
  actionLibraryExecutionExecuteParallel = '/gcp/actionLibraryExecution/executeParallel',
  actionLibraryExecutionExecuteChain = '/gcp/actionLibraryExecution/executeChain',
  actionLibraryExecutionGetExecutionStatus = '/gcp/actionLibraryExecution/getExecutionStatus',

  // 通知通道管理相关API
  notificationChannelGetAll = '/gcp/notificationChannel/getAll',
  notificationChannelGet = '/gcp/notificationChannel/get',
  notificationChannelSave = '/gcp/notificationChannel/save',
  notificationChannelDelete = '/gcp/notificationChannel/delete',
  notificationChannelGetChannelTypes = '/gcp/notificationChannel/getChannelTypes',
  notificationChannelTestChannel = '/gcp/notificationChannel/testChannel',

  // 通知动作相关API
  notificationActionSendNotification = '/gcp/notificationAction/sendNotification',
  notificationActionSendNotificationWithException = '/gcp/notificationAction/sendNotificationWithException',
  notificationActionGetAvailableChannels = '/gcp/notificationAction/getAvailableChannels',

  // Utils工具函数相关API
  utilsFunctionGetAll = '/gcp/utilsFunction/getAll',
  utilsFunctionGetByCategory = '/gcp/utilsFunction/getByCategory',

  // 脚本测试相关API
  scriptTest = '/gcp/script/test',
}

export const api = {
  run: (path: Services, args: any = {}) =>
    http.request('/gcp/function/run', {
      method: 'POST',
      body: {
        path,
        args,
      } as any,
    }),
};
